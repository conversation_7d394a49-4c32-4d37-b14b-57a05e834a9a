/**
 * 我打平台订单采集器
 * 用于从我打平台(fxdf.woda.com)采集订单数据
 */

import { showNotification } from '../../common/ui.js';
import { sendMessage } from '../../common/utils.js';

/**
 * 采集订单数据
 * @param {Object} options 选项
 * @returns {Promise<Object>} 订单数据
 */
export async function collectOrderData(options = {}) {
  try {
    console.log('开始采集我打平台订单数据...');
    
    // 显示加载提示
    showNotification('正在采集订单数据...', { type: 'info' });
    
    // 提取订单数据
    const orderData = extractOrderInfo();
    
    // 显示成功提示
    showNotification('订单数据采集成功', { type: 'success' });
    
    console.log('我打平台订单数据采集完成');
    
    return orderData;
  } catch (error) {
    console.error('采集我打平台订单数据失败:', error);
    showNotification(`采集失败: ${error.message}`, { type: 'error' });
    throw error;
  }
}

/**
 * 采集并发送订单数据
 * @param {Object} options 选项
 * @returns {Promise<Object>} 响应结果
 */
export async function collectAndSendOrderData(options = {}) {
  try {
    // 采集订单数据
    const orderData = await collectOrderData(options);
    
    // 发送数据到后台
    const response = await sendMessage({
      type: 'COLLECT_ORDER',
      data: orderData
    });
    
    return response;
  } catch (error) {
    console.error('采集并发送订单数据失败:', error);
    throw error;
  }
}

/**
 * 从当前页面提取订单信息
 * @returns {Object} 订单信息
 */
function extractOrderInfo() {
  try {
    const orders = [];
    
    // 获取当前选中的平台名称
    const selectedPlatformElement = document.querySelector('.platformSwitch .platformItem.active .platformName');
    const currentPlatform = selectedPlatformElement ? selectedPlatformElement.textContent?.trim() : null;
    
    // 选择所有订单行元素
    const orderRowElements = document.querySelectorAll('.vtrade-area .vtrade-row');
    
    orderRowElements.forEach(rowElement => {
      // 判断是否为合并订单
      const isMerged = rowElement.classList.contains('showDropdown');
      const type = isMerged ? 'merged' : 'single';
      
      // 提取收件人姓名
      const recipientNameElement = rowElement.querySelector('.copy-wrap a');
      const recipientName = recipientNameElement ? recipientNameElement.textContent?.trim() : null;
      
      // 提取收货地址和电话
      const addressPhoneContainer = rowElement.querySelector('.td.break-word.copyWrap.flex-box .flex-1');
      let shippingAddress = null;
      let recipientPhone = null;
      
      if (addressPhoneContainer) {
        // 提取地址
        const addressSpan = addressPhoneContainer.querySelector('span');
        if (addressSpan && addressSpan.textContent) {
          shippingAddress = addressSpan.textContent.trim();
        }
        
        // 提取电话
        const recipientPhoneElement = addressPhoneContainer.querySelector('span.c-info');
        recipientPhone = recipientPhoneElement ? recipientPhoneElement.textContent?.trim() : null;
        
        // 如果地址包含电话，则从地址中移除电话
        if (addressSpan && addressSpan.textContent && recipientPhone && addressSpan.textContent.includes(recipientPhone)) {
          shippingAddress = addressSpan.textContent.replace(recipientPhone, '').trim();
        }
      }
      
      // 提取订单总数量
      const totalQuantityElement = rowElement.querySelector('.td.bigger-count');
      const totalQuantityText = totalQuantityElement ? totalQuantityElement.textContent?.trim() : '0';
      const totalQuantity = parseInt(totalQuantityText, 10);
      const parsedTotalQuantity = isNaN(totalQuantity) ? null : totalQuantity;
      
      // 提取商品详情
      const productItems = rowElement.querySelectorAll('.order-item');
      const products = [];
      
      productItems.forEach(itemElement => {
        // 提取商品名称
        const productNameElement = itemElement.querySelector('p');
        const productName = productNameElement ? productNameElement.textContent?.trim() : null;
        
        // 提取商品数量
        const productQuantityElement = itemElement.querySelector('div.c-orangered');
        const quantityText = productQuantityElement ? productQuantityElement.textContent?.trim() : '0件';
        const quantity = parseInt(quantityText.replace('件', ''), 10);
        const productQuantity = isNaN(quantity) ? null : quantity;
        
        // 提取商品ID
        const productIdElement = itemElement.querySelector('div.c-999');
        const productIdText = productIdElement ? productIdElement.textContent?.trim() : null;
        const productIdMatch = productIdText ? productIdText.match(/商品ID:\s*(.*)/) : null;
        const productId = productIdMatch && productIdMatch[1] ? productIdMatch[1].trim() : null;
        
        // 提取商品图片URL
        const productImageElement = itemElement.querySelector('.wo-img img');
        const imageUrl = productImageElement?.getAttribute('data-src') || productImageElement?.getAttribute('src') || null;
        
        products.push({
          name: productName,
          quantity: productQuantity,
          id: productId,
          imageUrl: imageUrl
        });
      });
      
      // 构建订单对象并添加到结果中
      orders.push({
        type,
        orderSource: currentPlatform,
        recipientName,
        shippingAddress,
        recipientPhone,
        products,
        totalQuantity: parsedTotalQuantity
      });
    });
    
    // 构建最终结果
    return {
      platform: 'woda',
      type: 'order',
      timestamp: new Date().toISOString(),
      url: window.location.href,
      orders
    };
  } catch (error) {
    console.error('提取订单信息失败:', error);
    throw error;
  }
}

/**
 * 添加订单采集按钮
 */
export function addOrderCollectButton() {
  try {
    // 检查是否已存在按钮
    if (document.querySelector('.ylt-order-collect-button')) {
      return;
    }
    
    // 创建采集按钮
    const collectButton = document.createElement('button');
    collectButton.className = 'ylt-order-collect-button';
    collectButton.innerHTML = `
      <span class="ylt-icon">📋</span>
      <span class="ylt-text">采集订单</span>
    `;
    
    // 设置按钮样式
    Object.assign(collectButton.style, {
      position: 'fixed',
      top: '150px',
      right: '20px',
      zIndex: '9999',
      padding: '8px 12px',
      backgroundColor: '#59a588',
      color: 'white',
      border: 'none',
      borderRadius: '4px',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '5px',
      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)'
    });
    
    // 添加悬停效果
    collectButton.addEventListener('mouseover', () => {
      collectButton.style.backgroundColor = '#4a8a72';
    });
    
    collectButton.addEventListener('mouseout', () => {
      collectButton.style.backgroundColor = '#59a588';
    });
    
    // 添加点击事件
    collectButton.addEventListener('click', () => {
      collectAndSendOrderData();
    });
    
    // 添加到页面
    document.body.appendChild(collectButton);
    
    console.log('订单采集按钮已添加');
  } catch (error) {
    console.error('添加订单采集按钮失败:', error);
  }
}
