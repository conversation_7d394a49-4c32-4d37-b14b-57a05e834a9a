/**
 * Woda平台内容脚本
 * 负责在Woda平台页面注入UI元素和处理用户交互
 */

import { detectPageType, getCurrentPlatform, sendMessage } from '../../common/utils.js';
import { showNotification, createFloatingButton, createToolbar, createButton, createDataPreviewPanel } from '../../common/ui.js';
import { checkAndHandleAuthorization } from '../../common/auth.js';
import { collectProductData, collectShopData, collectOrderData } from './collector.js';
import { addOrderCollectButton } from './wodaOrderCollector.js';

// 当前平台
const PLATFORM = 'woda';

// 页面类型
let pageType = 'unknown';

// UI元素
let floatingButton = null;
let toolbar = null;

/**
 * 初始化
 */
async function initialize() {
  console.log('初始化Woda平台内容脚本...');

  // 检测页面类型
  pageType = detectPageType(window.location.href, PLATFORM);
  console.log(`当前页面类型: ${pageType}`);

  // 检查授权
  const authorized = await checkAndHandleAuthorization();

  if (!authorized) {
    console.log('用户未授权，不注入UI元素');
    return;
  }

  // 注入标准UI元素
  injectUI();

  // 检测是否为我打分销平台页面
  if (window.location.href.includes('fxdf.woda.com')) {
    console.log('检测到我打分销平台页面');
    console.log('当前URL:', window.location.href);
    console.log('页面状态:', document.readyState);
    console.log('平台切换元素:', !!document.querySelector('.platformSwitch'));
    console.log('订单行元素:', !!document.querySelectorAll('.vtrade-area .vtrade-row').length);

    // 等待页面完全加载
    if (document.readyState === 'complete') {
      addOrderCollectButtonWithRetry();
    } else {
      window.addEventListener('load', addOrderCollectButtonWithRetry);
    }
  }

  console.log('Woda平台内容脚本初始化完成');
}

/**
 * 添加订单采集按钮（带重试）
 */
function addOrderCollectButtonWithRetry(maxRetries = 5, delay = 1000) {
  let retries = 0;

  function tryAddButton() {
    try {
      // 导入并调用添加按钮函数
      import('./wodaOrderCollector.js').then(module => {
        module.addOrderCollectButton();
        console.log('订单采集按钮添加成功');
      }).catch(error => {
        console.error('加载订单采集模块失败:', error);

        // 重试
        if (retries < maxRetries) {
          retries++;
          console.log(`尝试重新添加订单采集按钮 (${retries}/${maxRetries})...`);
          setTimeout(tryAddButton, delay);
        }
      });
    } catch (error) {
      console.error('添加订单采集按钮失败:', error);

      // 重试
      if (retries < maxRetries) {
        retries++;
        console.log(`尝试重新添加订单采集按钮 (${retries}/${maxRetries})...`);
        setTimeout(tryAddButton, delay);
      }
    }
  }

  // 开始尝试添加按钮
  tryAddButton();
}

/**
 * 检测是否为我打分销平台订单页面
 * @returns {boolean} 是否为我打分销平台订单页面
 */
function isWodaDistributionOrderPage() {
  // 检查URL是否包含fxdf.woda.com
  const isWodaDistributionUrl = window.location.href.includes('fxdf.woda.com');

  // 检查URL是否包含SupplierTrades或trade关键词
  const isTradeUrl = window.location.href.includes('SupplierTrades') ||
                     window.location.href.includes('/trade') ||
                     window.location.href.includes('#/trade');

  // 检查是否存在平台切换元素（放宽条件，只要是我打分销平台即可）
  const hasPlatformSwitch = !!document.querySelector('.platformSwitch') ||
                           !!document.querySelector('.platformItem') ||
                           !!document.querySelector('[class*="platform"]');

  // 简化判断条件，只要是我打分销平台的页面就返回true
  return isWodaDistributionUrl && (isTradeUrl || hasPlatformSwitch);
}

/**
 * 注入UI元素
 */
function injectUI() {
  // 创建浮动按钮
  createFloatingUI();

  // 根据页面类型注入特定UI
  switch (pageType) {
    case 'product':
      injectProductUI();
      break;
    case 'shop':
      injectShopUI();
      break;
    case 'order':
      injectOrderUI();
      break;
  }
}

/**
 * 创建浮动UI
 */
function createFloatingUI() {
  // 创建浮动按钮
  floatingButton = createFloatingButton({
    icon: '<span style="font-size: 24px;">🛒</span>',
    position: 'bottom-right',
    onClick: toggleToolbar
  });

  // 创建工具栏
  toolbar = createToolbar({
    title: '衣联通采购助手',
    visible: false
  });

  // 添加工具栏内容
  updateToolbarContent();

  // 添加到页面
  document.body.appendChild(floatingButton);
  document.body.appendChild(toolbar.element);
}

/**
 * 更新工具栏内容
 */
function updateToolbarContent() {
  // 清空内容
  toolbar.content.innerHTML = '';

  // 添加标题
  const title = document.createElement('div');
  title.style.fontSize = '16px';
  title.style.fontWeight = 'bold';
  title.style.marginBottom = '15px';
  title.textContent = '选择操作';

  // 添加按钮
  const buttonContainer = document.createElement('div');
  buttonContainer.style.display = 'flex';
  buttonContainer.style.flexDirection = 'column';
  buttonContainer.style.gap = '10px';

  // 根据页面类型添加不同的按钮
  switch (pageType) {
    case 'product':
      buttonContainer.appendChild(createButton('采集商品', {
        type: 'primary',
        fullWidth: true,
        icon: '🛍️',
        onClick: collectProduct
      }));
      break;
    case 'shop':
      buttonContainer.appendChild(createButton('采集店铺', {
        type: 'primary',
        fullWidth: true,
        icon: '🏪',
        onClick: collectShop
      }));

      buttonContainer.appendChild(createButton('批量采集商品', {
        type: 'warning',
        fullWidth: true,
        icon: '📦',
        onClick: batchCollectProducts
      }));
      break;
    case 'order':
      buttonContainer.appendChild(createButton('采集订单', {
        type: 'primary',
        fullWidth: true,
        icon: '📋',
        onClick: collectOrder
      }));
      break;
  }

  // 如果是我打分销平台，添加采集订单按钮
  if (window.location.href.includes('fxdf.woda.com')) {
    buttonContainer.appendChild(createButton('采集订单', {
      type: 'primary',
      fullWidth: true,
      icon: '📋',
      onClick: () => {
        // 导入并调用采集订单函数
        import('./wodaOrderCollector.js').then(module => {
          module.collectAndSendOrderData();
        }).catch(error => {
          console.error('加载订单采集模块失败:', error);
          showNotification('加载订单采集模块失败', { type: 'error' });
        });
      }
    }));
  }

  // 添加通用按钮
  buttonContainer.appendChild(createButton('查看采集历史', {
    type: 'secondary',
    fullWidth: true,
    icon: '📋',
    onClick: viewCollectionHistory
  }));

  buttonContainer.appendChild(createButton('设置', {
    type: 'secondary',
    fullWidth: true,
    icon: '⚙️',
    onClick: openSettings
  }));

  // 添加到工具栏
  toolbar.content.appendChild(title);
  toolbar.content.appendChild(buttonContainer);
}

/**
 * 切换工具栏显示状态
 */
function toggleToolbar() {
  toolbar.toggle();
}

/**
 * 注入商品页面UI
 */
function injectProductUI() {
  // 创建采集按钮
  const collectButton = document.createElement('button');
  collectButton.className = 'ylt-collect-button';
  collectButton.innerHTML = `
    <span class="ylt-icon">🛍️</span>
    <span class="ylt-text">采集商品</span>
  `;

  // 设置按钮样式
  Object.assign(collectButton.style, {
    position: 'fixed',
    top: '150px',
    right: '20px',
    zIndex: '9999'
  });

  // 添加点击事件
  collectButton.addEventListener('click', collectProduct);

  // 添加到页面
  document.body.appendChild(collectButton);
}

/**
 * 注入店铺页面UI
 */
function injectShopUI() {
  // 创建采集店铺按钮
  const collectShopButton = document.createElement('button');
  collectShopButton.className = 'ylt-collect-button';
  collectShopButton.innerHTML = `
    <span class="ylt-icon">🏪</span>
    <span class="ylt-text">采集店铺</span>
  `;

  // 设置按钮样式
  Object.assign(collectShopButton.style, {
    position: 'fixed',
    top: '150px',
    right: '20px',
    zIndex: '9999'
  });

  // 添加点击事件
  collectShopButton.addEventListener('click', collectShop);

  // 创建批量采集按钮
  const batchCollectButton = document.createElement('button');
  batchCollectButton.className = 'ylt-collect-button';
  batchCollectButton.innerHTML = `
    <span class="ylt-icon">📦</span>
    <span class="ylt-text">批量采集商品</span>
  `;

  // 设置按钮样式
  Object.assign(batchCollectButton.style, {
    position: 'fixed',
    top: '200px',
    right: '20px',
    zIndex: '9999'
  });

  // 添加点击事件
  batchCollectButton.addEventListener('click', batchCollectProducts);

  // 添加到页面
  document.body.appendChild(collectShopButton);
  document.body.appendChild(batchCollectButton);
}

/**
 * 注入订单页面UI
 */
function injectOrderUI() {
  // 创建采集订单按钮
  const collectOrderButton = document.createElement('button');
  collectOrderButton.className = 'ylt-collect-button';
  collectOrderButton.innerHTML = `
    <span class="ylt-icon">📋</span>
    <span class="ylt-text">采集订单</span>
  `;

  // 设置按钮样式
  Object.assign(collectOrderButton.style, {
    position: 'fixed',
    top: '150px',
    right: '20px',
    zIndex: '9999'
  });

  // 添加点击事件
  collectOrderButton.addEventListener('click', collectOrder);

  // 添加到页面
  document.body.appendChild(collectOrderButton);
}

/**
 * 采集商品
 */
async function collectProduct() {
  try {
    // 显示加载中通知
    showNotification('正在采集商品数据...', { type: 'info' });

    // 采集商品数据
    const productData = await collectProductData();

    // 显示数据预览
    createDataPreviewPanel(productData, {
      title: '商品数据预览',
      onConfirm: async (data) => {
        try {
          // 显示上传中通知
          showNotification('正在上传商品数据...', { type: 'info' });

          // 发送数据到后台
          const response = await sendMessage({
            action: 'uploadData',
            data: {
              type: 'product',
              platform: PLATFORM,
              content: data
            }
          });

          // 显示成功通知
          showNotification('商品数据上传成功', { type: 'success' });

          // 如果有重定向URL，跳转到该URL
          if (response && response.redirectUrl) {
            window.open(response.redirectUrl, '_blank');
          }
        } catch (error) {
          console.error('上传商品数据失败:', error);
          showNotification(`上传失败: ${error.message}`, { type: 'error' });
        }
      },
      onCancel: () => {
        showNotification('已取消上传', { type: 'info' });
      }
    });
  } catch (error) {
    console.error('采集商品失败:', error);
    showNotification(`采集失败: ${error.message}`, { type: 'error' });
  }
}

/**
 * 采集店铺
 */
async function collectShop() {
  try {
    // 显示加载中通知
    showNotification('正在采集店铺数据...', { type: 'info' });

    // 采集店铺数据
    const shopData = await collectShopData();

    // 显示数据预览
    createDataPreviewPanel(shopData, {
      title: '店铺数据预览',
      onConfirm: async (data) => {
        try {
          // 显示上传中通知
          showNotification('正在上传店铺数据...', { type: 'info' });

          // 发送数据到后台
          const response = await sendMessage({
            action: 'uploadData',
            data: {
              type: 'shop',
              platform: PLATFORM,
              content: data
            }
          });

          // 显示成功通知
          showNotification('店铺数据上传成功', { type: 'success' });

          // 如果有重定向URL，跳转到该URL
          if (response && response.redirectUrl) {
            window.open(response.redirectUrl, '_blank');
          }
        } catch (error) {
          console.error('上传店铺数据失败:', error);
          showNotification(`上传失败: ${error.message}`, { type: 'error' });
        }
      },
      onCancel: () => {
        showNotification('已取消上传', { type: 'info' });
      }
    });
  } catch (error) {
    console.error('采集店铺失败:', error);
    showNotification(`采集失败: ${error.message}`, { type: 'error' });
  }
}

/**
 * 批量采集商品
 */
async function batchCollectProducts() {
  try {
    // 显示确认对话框
    if (!confirm('确定要批量采集该店铺的商品吗？这可能需要一些时间。')) {
      return;
    }

    // 显示加载中通知
    showNotification('正在批量采集商品数据...', { type: 'info' });

    // 发送批量采集请求到后台
    const response = await sendMessage({
      action: 'batchCollectProducts',
      data: {
        platform: PLATFORM,
        url: window.location.href
      }
    });

    // 显示成功通知
    showNotification(`批量采集任务已启动，共 ${response.totalProducts} 个商品`, { type: 'success' });
  } catch (error) {
    console.error('批量采集商品失败:', error);
    showNotification(`批量采集失败: ${error.message}`, { type: 'error' });
  }
}

/**
 * 采集订单
 */
async function collectOrder() {
  try {
    // 显示加载中通知
    showNotification('正在采集订单数据...', { type: 'info' });

    // 采集订单数据
    const orderData = await collectOrderData();

    // 显示数据预览
    createDataPreviewPanel(orderData, {
      title: '订单数据预览',
      onConfirm: async (data) => {
        try {
          // 显示上传中通知
          showNotification('正在上传订单数据...', { type: 'info' });

          // 发送数据到后台
          const response = await sendMessage({
            action: 'uploadData',
            data: {
              type: 'order',
              platform: PLATFORM,
              content: data
            }
          });

          // 显示成功通知
          showNotification('订单数据上传成功', { type: 'success' });

          // 如果有重定向URL，跳转到该URL
          if (response && response.redirectUrl) {
            window.open(response.redirectUrl, '_blank');
          }
        } catch (error) {
          console.error('上传订单数据失败:', error);
          showNotification(`上传失败: ${error.message}`, { type: 'error' });
        }
      },
      onCancel: () => {
        showNotification('已取消上传', { type: 'info' });
      }
    });
  } catch (error) {
    console.error('采集订单失败:', error);
    showNotification(`采集失败: ${error.message}`, { type: 'error' });
  }
}

/**
 * 查看采集历史
 */
async function viewCollectionHistory() {
  try {
    // 发送查看历史请求到后台
    await sendMessage({
      action: 'openCollectionHistory'
    });
  } catch (error) {
    console.error('查看采集历史失败:', error);
    showNotification(`查看历史失败: ${error.message}`, { type: 'error' });
  }
}

/**
 * 打开设置
 */
async function openSettings() {
  try {
    // 发送打开设置请求到后台
    await sendMessage({
      action: 'openSettings'
    });
  } catch (error) {
    console.error('打开设置失败:', error);
    showNotification(`打开设置失败: ${error.message}`, { type: 'error' });
  }
}

// 初始化
initialize();
